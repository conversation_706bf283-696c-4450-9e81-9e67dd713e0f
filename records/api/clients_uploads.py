import datetime
import mimetypes
import os
from typing import Optional, List, Dict

from fastapi import APIRouter, HTTPException
from fastapi import Form
from fastapi import Response
from fastapi import UploadFile
from fastapi.responses import ORJSONResponse
from pydantic import BaseModel
from starlette.responses import StreamingResponse

from records.cache import global_cache
from records.config.shared_config import SharedConfig
from records.context import context as ctx
from records.db import api as db_api, base, models
from records.services import detections
from records.services.client_upload_service import get_client_upload_service
from records.utils import utils


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'client_uploads'))

    router.add_api_route("", list_client_uploads, methods=['GET'], name='List client uploads')
    router.add_api_route("", upload_client_upload, methods=['POST'], name='Create client upload')
    router.add_api_route("/{upload_id}", get_client_upload, methods=['GET'], name='Get client upload')
    router.add_api_route("/{upload_id}/download", get_client_upload_download, methods=['GET'], name='Download client upload')
    router.add_api_route("/{upload_id}", update_client_upload, methods=['PUT'], name='Update client upload')
    router.add_api_route("/{upload_id}/action", client_upload_action, methods=['PUT'], name='Do action on client upload')
    router.add_api_route("/{upload_id}", delete_client_upload, methods=['DELETE'], name='Delete client upload')

    return router


default_result = [{'output': 'Unknown error'}]


async def list_client_uploads(
    limit: int = 25,
    page: int = 1,
    order: str = 'id',
    desc: bool = True,
    q: str = None,
    applied: bool = False,
    all: bool = False,
):
    """List client uploads with current status and file information."""
    async with base.session_context():
        # Get uploads with updated statuses
        upload_dicts, count = await get_client_upload_service().list_uploads_with_status(
            limit=limit, page=page, order=order, desc=desc, q=q, applied=applied, all=all
        )

        # Merge with client data for EIN matching
        upload_dicts = await get_client_upload_service().merge_uploads_with_clients(upload_dicts)

    upload_dicts = await prepare_uploads(upload_dicts)

    return ORJSONResponse({
        'items': upload_dicts, 'count': count, 'limit': limit, 'page': page
    })


async def get_client_upload(upload_id: int):
    """Get single client upload with current status and merged data."""
    async with base.session_context():
        # Get upload with current status
        upload_dict = await get_client_upload_service().get_upload_with_status(upload_id)

        # Merge with client data
        upload_dicts = await get_client_upload_service().merge_uploads_with_clients([upload_dict])
        upload_dict = upload_dicts[0]

    upload_dict['download_link'] = await get_client_upload_service().generate_download_link(upload_dict['file_id'])
    upload_dict = (
        await prepare_uploads([upload_dict])
    )[0]

    return ORJSONResponse(upload_dict)


async def get_client_upload_download(upload_id: int, inline: bool = False):
    async with base.session_context():
        upload_db = await db_api.get_client_upload_by_id(upload_id)

        file_db = await db_api.get_client_file_by_id(upload_db.file_id)

    return await download_file(file_db=file_db, inline=inline)


async def download_file(file_id: str = None, file_db: models.File = None, inline: bool = False):
    if not file_db:
        if not file_id:
            raise HTTPException(400, "Either file_id or file_db must be provided")
        file_db = await db_api.get_client_file_by_id(file_id)

    file_path = detections.get_upload_file_path(file_db)

    read_file = await SharedConfig().file_manager.read_file(file_path)
    stream = SharedConfig().file_manager.get_data_generator(read_file)
    file_name = file_db.name.encode('latin-1', 'replace').decode('latin-1')
    content_disposition = f'attachment; filename="{file_name}"' if not inline else f'inline; filename="{file_name}"'
    content_length = file_db.size
    return StreamingResponse(
        stream,
        media_type=file_db.file_type,
        headers={
            'Content-Disposition': content_disposition,
            'Content-Length': str(content_length)
        }
    )


class AttachedObject(BaseModel):
    object_type: str
    object_id: str


class ClientUploadUpdate(BaseModel):
    attached_objects: Optional[List[AttachedObject]] = None
    # Legacy fields for backward compatibility (deprecated)
    attach_object_id: Optional[str] = None
    attach_object_type: Optional[str] = None

    date: Optional[datetime.datetime] = None
    description: Optional[str] = None
    doc_type: Optional[str] = None
    overridden_client_data: Optional[dict] = None


async def update_client_upload(upload_id: int, update: ClientUploadUpdate):
    async with base.session_context():
        upload_db = await db_api.get_client_upload_by_id(upload_id)
        update_dict = update.model_dump(exclude_unset=True)
        if not update_dict:
            raise HTTPException(400, 'No fields to update')

        update_dict = await get_client_upload_service().update_upload(upload_db, update_dict)

    update_dict = await prepare_uploads([update_dict])[0]

    return ORJSONResponse(update_dict)


async def upload_client_upload(
    file: UploadFile,
    description: Optional[str] = Form(None),
):
    """Upload a new client file and start processing."""
    async with base.session_context():
        file_data = file.file.read()
        file_name = file.filename
        manager = ctx.current_manager()

        file_dict = {
            'name': file_name,
            'size': len(file_data),
            'hash': utils.hash_sha256(file_data),
            'file_type': mimetypes.guess_type(file_name)[0],
            'manager_id': manager.id,
            'description': description,
            'from_upload': True,
        }

        # Check for duplicate
        existing_file = await db_api.get_client_file_by_hash(None, file_dict['hash'], notfoundok=True)
        if existing_file:
            raise HTTPException(409, f"File already exists with name {existing_file.name}")

        # Create file and save to storage
        file_db = await db_api.create_client_file(file_dict)
        await SharedConfig().file_manager.save_file(detections.get_upload_file_path(file_db), file_data)

        # Create upload and start processing
        upload_db = await get_client_upload_service().create_upload(
            file_db=file_db,
            file_data=file_data,
            manager_id=manager.id
        )

    upload_dict = upload_db.to_dict()
    upload_dict['file'] = file_db.to_dict()

    return ORJSONResponse(upload_dict)


async def delete_client_upload(upload_id: int):
    async with base.session_context():
        await get_client_upload_service().delete_client_upload(
            upload_id=upload_id,
            delete_file_db=True,
            deletion_behavior=models.ClientUploadDeletionBehavior.DEFAULT
        )

    return Response(status_code=204)


class ClientUploadAction(BaseModel):
    action: str
    overridden_client_data: Optional[dict] = None


async def client_upload_action(
    upload_id: int,
    action: ClientUploadAction,
):
    async with base.session_context():
        if action.action.lower() == 'apply':
            upload_dict = await get_client_upload_service().apply_upload(
                upload_id,
                overridden_client_data=action.overridden_client_data
            )
            return ORJSONResponse(upload_dict)
        elif action.action.lower() == 'remerge':
            upload_dict = await get_client_upload_service().remerge_upload(upload_id)
            return ORJSONResponse(upload_dict)
        else:
            raise HTTPException(400, f"Unknown action {action.action}")


async def prepare_uploads(upload_dicts: List[Dict]) -> List[Dict]:
    """Prepare uploads for API response by adding extra fields."""
    for upload_dict in upload_dicts:
        del upload_dict['detection_items_cache']
    return upload_dicts
