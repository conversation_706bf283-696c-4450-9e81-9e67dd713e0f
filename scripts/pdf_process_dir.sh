#!/bin/bash

dir=$1
output_dir=$2
rest=${@:3}

if [ -z "$dir" ] || [ -z "$output_dir" ];
then
  echo "Usage $0 <dir> <output_dir>"
  exit 1
fi

# Create output dir if it does not exist
mkdir -p $output_dir

all_files=$(find $dir -type f)

OIFS="$IFS"
IFS=$'\n'
for file in $all_files
do
    echo "Processing $file"
    base=$(echo $file | cut -d '/' -f 7)
    python scripts/pdf_process.py --img-dir imgs --workers 4 --output $output_dir/$base.md $file $rest
done

IFS="$OIFS"
